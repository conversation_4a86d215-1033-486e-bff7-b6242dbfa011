import requests
from bs4 import BeautifulSoup
import csv
import time
import random
import re

class DoubanTop250Spider:
    def __init__(self):
        self.base_url = "https://movie.douban.com/top250"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.cookies = {
            'bid': 'cXcGizqt3H8',
            '_pk_id.100001.4cf6': '0403a81eb172835d.**********.',
            '__yadk_uid': 'rrn6oHYyXTL0nl3zEXfZjP5EO0IYALmA',
            '_vwo_uuid_v2': 'D3362307678DB187F216F3F6EEE5EDFB3|5ff1fe2913798b0490910fa50e1190c6',
            'viewed': '"3283913_6390767"',
            'dbcl2': '"*********:iPcKwxTnMZU"',
            'ck': '1Z4R',
            '_pk_ref.100001.4cf6': '%5B%22%22%2C%22%22%2C**********%2C%22https%3A%2F%2Faccounts.douban.com%2F%22%5D',
            '_pk_ses.100001.4cf6': '1',
            '__utma': '*********.*********.**********.**********.**********.7',
            '__utmb': '*********.0.10.**********',
            '__utmc': '*********',
            '__utmz': '*********.**********.7.2.utmcsr=accounts.douban.com|utmccn=(referral)|utmcmd=referral|utmcct=/',
            'ap_v': '0,6.0',
            'push_noty_num': '0',
            'push_doumail_num': '0',
        }
        self.movies = []

    def get_page_data(self, start=0):
        """获取单页数据"""
        url = f"{self.base_url}?start={start}&filter="

        try:
            response = requests.get(url, headers=self.headers, cookies=self.cookies)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')
            movie_items = soup.find_all('div', class_='item')

            page_movies = []
            for item in movie_items:
                movie_info = self.parse_movie_item(item)
                if movie_info:
                    page_movies.append(movie_info)

            print(f"成功获取第{start//25 + 1}页数据，共{len(page_movies)}部电影")
            return page_movies

        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return []

    def parse_movie_item(self, item):
        """解析单个电影信息"""
        try:
            # 电影名称
            title_element = item.find('span', class_='title')
            title = title_element.text.strip() if title_element else ""

            # 导演和演员信息
            info_element = item.find('div', class_='bd').find('p')
            info_text = info_element.text.strip() if info_element else ""

            # 提取导演
            director_match = re.search(r'导演:\s*([^主]*?)(?:\s+主演|$)', info_text)
            director = director_match.group(1).strip() if director_match else ""

            # 提取年份和国家信息
            year_country_element = info_element.find_all('br')
            if year_country_element:
                year_country_text = year_country_element[0].next_sibling
                if year_country_text:
                    year_match = re.search(r'(\d{4})', year_country_text)
                    year = year_match.group(1) if year_match else ""
                else:
                    year = ""
            else:
                year = ""

            # 评分
            rating_element = item.find('span', class_='rating_num')
            rating = rating_element.text.strip() if rating_element else ""

            # 评价人数
            rating_people_element = item.find('div', class_='star').find_all('span')
            rating_people = ""
            for span in rating_people_element:
                if '人评价' in span.text:
                    rating_people = span.text.replace('人评价', '').strip()
                    break

            return {
                '电影名称': title,
                '导演': director,
                '年份': year,
                '评分': rating,
                '评价人数': rating_people
            }

        except Exception as e:
            print(f"解析电影信息失败: {e}")
            return None

    def crawl_all_movies(self):
        """爬取所有250部电影"""
        print("开始爬取豆瓣Top250电影数据...")

        # 豆瓣Top250共10页，每页25部电影
        for page in range(10):
            start = page * 25
            page_movies = self.get_page_data(start)
            self.movies.extend(page_movies)

            # 随机延时，避免被反爬
            time.sleep(random.uniform(1, 3))

        print(f"爬取完成！共获取{len(self.movies)}部电影数据")
        return self.movies

    def save_to_csv(self, filename='douban_top250.csv'):
        """保存数据到CSV文件"""
        if not self.movies:
            print("没有数据可保存")
            return

        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['电影名称', '导演', '年份', '评分', '评价人数']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for movie in self.movies:
                    writer.writerow(movie)

            print(f"数据已保存到 {filename}")

        except Exception as e:
            print(f"保存CSV文件失败: {e}")

    def print_sample_data(self, num=5):
        """打印前几条数据作为示例"""
        if not self.movies:
            print("没有数据可显示")
            return

        print(f"\n前{num}部电影数据示例:")
        print("-" * 80)
        for i, movie in enumerate(self.movies[:num], 1):
            print(f"{i}. {movie['电影名称']}")
            print(f"   导演: {movie['导演']}")
            print(f"   年份: {movie['年份']}")
            print(f"   评分: {movie['评分']}")
            print(f"   评价人数: {movie['评价人数']}")
            print("-" * 80)

def main():
    spider = DoubanTop250Spider()

    # 爬取所有电影数据
    spider.crawl_all_movies()

    # 显示示例数据
    spider.print_sample_data()

    # 保存到CSV文件
    spider.save_to_csv()

if __name__ == "__main__":
    main()